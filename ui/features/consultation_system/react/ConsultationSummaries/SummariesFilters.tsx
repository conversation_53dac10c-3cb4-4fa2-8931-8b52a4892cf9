import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import { Grid } from '@instructure/ui-grid'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationFilters } from '../types'

interface SummariesFiltersProps {
  filters: ConsultationFilters
  concernTypes: string[]
  onFiltersChange: (filters: ConsultationFilters) => void
  loading: boolean
}

const SummariesFilters: React.FC<SummariesFiltersProps> = ({
  filters,
  concernTypes,
  onFiltersChange,
  loading
}) => {
  const [localFilters, setLocalFilters] = useState<ConsultationFilters>(filters)

  const handleFilterChange = (key: keyof ConsultationFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
  }

  const handleClearFilters = () => {
    const clearedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const hasActiveFilters = Object.values(localFilters).some(value => value && value !== '')

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 medium 0">
      <Grid>
        <Grid.Row>
          <Grid.Col width={3} style={{ padding: 0 }}>
            <Select
              padding="0"
              width='100%'
              renderLabel="Concern Type"
              placeholder="All types"
              value={localFilters.concern_type || ''}
              onChange={(e: any, { value }: any) => handleFilterChange('concern_type', value as string)}
            >
              <Select.Option id="all-types" value="">
                All Types
              </Select.Option>
              {concernTypes.map(type => (
                <Select.Option key={type} id={type} value={type}>
                  {type}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          
          <Grid.Col width={3} style={{ padding: 0 }}>
            <TextInput
              renderLabel="Search Students"
              placeholder="Student name or ID"
              value={localFilters.student_search || ''}
              onChange={(e) => handleFilterChange('student_search', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <TextInput
              renderLabel="Search Content"
              placeholder="Notes, outcomes, etc."
              value={localFilters.content_search || ''}
              onChange={(e) => handleFilterChange('content_search', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <View as="div" display="flex" height="100%">
              <Button
                color="primary"
                renderIcon={IconSearchLine}
                onClick={handleApplyFilters}
                disabled={loading}
              >
                Search
              </Button>
              {hasActiveFilters && (
                <Button
                  renderIcon={IconXLine}
                  onClick={handleClearFilters}
                  disabled={loading}
                >
                  Clear
                </Button>
              )}
            </View>
          </Grid.Col>
        </Grid.Row>
        
        <Grid.Row>
          <Grid.Col width={3}>
            <View as="div">
              <label htmlFor="start-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
                Start Date
              </label>
              <input
                id="start-date-input"
                type="date"
                value={localFilters.start_date || ''}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #C7CDD1',
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  backgroundColor: '#FFFFFF'
                }}
              />
            </View>
          </Grid.Col>

          <Grid.Col width={3}>
            <View as="div">
              <label htmlFor="end-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
                End Date
              </label>
              <input
                id="end-date-input"
                type="date"
                value={localFilters.end_date || ''}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #C7CDD1',
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  backgroundColor: '#FFFFFF'
                }}
              />
            </View>
          </Grid.Col>
          
          <Grid.Col width={6}>
            <Flex
              as="div"
              justifyItems="start"
              alignItems="center"
              gap="medium"
              style={{ marginRight: '1rem' }}
            >
              <label style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  style={{ margin: 0 }}
                  type="checkbox"
                  checked={localFilters.with_referrals === 'true'}
                  onChange={(e) => handleFilterChange('with_referrals', e.target.checked ? 'true' : '')}
                />
                <span style={{ marginLeft: '0.5rem' }}>With Referrals</span>
              </label>
              <label style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  style={{ margin: 0 }}
                  type="checkbox"
                  checked={localFilters.requires_follow_up === 'true'}
                  onChange={(e) => handleFilterChange('requires_follow_up', e.target.checked ? 'true' : '')}
                />
                <span style={{ marginLeft: '0.5rem' }}>Requires Follow-up</span>
              </label>
            </Flex>
          </Grid.Col>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default SummariesFilters
